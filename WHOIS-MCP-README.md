# 🔍 WHOIS MCP Server - Проверка доменов через MCP

Полнофункциональный MCP (Model Context Protocol) сервер для проверки доменов через whois с веб-интерфейсом и SSE поддержкой.

## 🎯 Возможности

### 🔧 MCP Tools
- **`check-domain-whois`** - Проверка информации о домене через whois
  - Получение даты создания и истечения домена
  - Информация о регистраторе
  - DNS серверы
  - Статус домена
  - Полный whois вывод

### 🌐 Веб-интерфейс
- **Красивый Bootstrap интерфейс** для проверки доменов
- **Быстрые кнопки** для популярных доменов
- **Парсинг результатов** с выделением ключевой информации
- **Полный whois вывод** в удобном формате

### 📡 API Endpoints
- **GET /api/whois/:domain** - REST API для проверки домена
- **POST /mcp** - MCP JSON-RPC endpoint
- **GET /mcp** - SSE endpoint для MCP клиентов

## 📁 Файлы проекта

```
├── whois-mcp-server.js          # Основной WHOIS MCP сервер с ngrok
├── whois-local-server.js        # Локальный WHOIS сервер (без ngrok)
├── whois-checker.html           # Веб-интерфейс для проверки доменов
├── ngrok-server.js              # Обновленный основной сервер с WHOIS
└── WHOIS-MCP-README.md          # Эта документация
```

## 🚀 Запуск серверов

### 1. Основной сервер с ngrok (РЕКОМЕНДУЕТСЯ)
```bash
# Запуск основного сервера с WHOIS функциональностью
NGROK_AUTHTOKEN=************************************************* node ngrok-server.js

# Доступные URL:
# 🏠 Главная: http://localhost:3000
# 🔍 WHOIS: http://localhost:3000/whois
# 📡 API: http://localhost:3000/api/whois/example.com
# 🌍 Публичный: https://your-ngrok-url.ngrok-free.app/whois
```

### 2. Отдельный WHOIS сервер с ngrok
```bash
# Запуск отдельного WHOIS сервера (порт 3001)
NGROK_AUTHTOKEN=************************************************* node whois-mcp-server.js
```

### 3. Локальный WHOIS сервер (без ngrok)
```bash
# Запуск локального сервера для тестирования
node whois-local-server.js

# Доступные URL:
# 🏠 Главная: http://localhost:3001
# 🔍 WHOIS: http://localhost:3001/whois
# 📡 API: http://localhost:3001/api/whois/example.com
```

## 🔧 Конфигурация MCP клиентов

### Полная конфигурация с Context7 и WHOIS
```json
{
  "mcpServers": {
    "context7-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@upstash/context7-mcp",
        "--key",
        "d598d734-db1b-4af8-9b70-b2c1d9bbd988"
      ]
    },
    "services_with_whois": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "https://ff50-107-174-70-218.ngrok-free.app/mcp"]
    },
    "whois_local": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "http://localhost:3001/mcp"]
    }
  }
}
```

### Только WHOIS серверы
```json
{
  "mcpServers": {
    "services_with_whois": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "https://your-ngrok-url.ngrok-free.app/mcp"]
    },
    "whois_local": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "http://localhost:3001/mcp"]
    }
  }
}
```

## 🧪 Тестирование

### 1. Тест API через curl
```bash
# Проверка домена через API
curl "http://localhost:3000/api/whois/google.com"

# Локальный сервер
curl "http://localhost:3001/api/whois/github.com"
```

### 2. Тест MCP tool через JSON-RPC
```bash
curl -X POST http://localhost:3000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "check-domain-whois",
      "arguments": {"domain": "example.com"}
    }
  }'
```

### 3. Веб-интерфейс
- Откройте `http://localhost:3000/whois` в браузере
- Введите домен для проверки
- Используйте быстрые кнопки для популярных доменов

## 📊 Примеры использования

### MCP Tool в AI чате
```
Используй инструмент check-domain-whois для проверки домена google.com
```

### API запрос
```javascript
const response = await fetch('/api/whois/example.com');
const data = await response.json();
console.log(data.data.parsed.creationDate);
```

### Веб-интерфейс
1. Откройте страницу WHOIS проверки
2. Введите домен (например: `github.com`)
3. Нажмите "Проверить домен"
4. Просмотрите результаты в удобном формате

## 🔍 Парсинг WHOIS данных

Сервер автоматически парсит следующие поля:
- **Domain Name** - Имя домена
- **Creation Date** - Дата создания
- **Registry Expiry Date** - Дата истечения
- **Registrar** - Регистратор
- **Name Server** - DNS серверы
- **Domain Status** - Статус домена
- **DNSSEC** - Статус DNSSEC
- **Registrar Abuse Contact** - Контакты для жалоб

## 🛠️ Требования

### Системные требования
```bash
# Установка whois (если не установлен)
sudo apt install whois

# Проверка установки
whois --version
```

### Node.js зависимости
```bash
npm install express cors @ngrok/ngrok
```

## 🌟 Особенности

### ✅ Что работает
- ✅ Проверка .com, .net, .org, .ru и других доменов
- ✅ Парсинг основных полей whois
- ✅ Красивый веб-интерфейс с Bootstrap
- ✅ MCP интеграция с JSON-RPC
- ✅ SSE поддержка для real-time уведомлений
- ✅ REST API для внешних приложений
- ✅ Автоматический поиск whois команды
- ✅ Таймауты и обработка ошибок

### 🔧 Настройки
- **Таймаут whois запроса:** 30 секунд
- **Порты:** 3000 (основной), 3001 (локальный)
- **Поддерживаемые пути whois:** `/usr/bin/whois`, `/bin/whois`, `whois`

## 🆘 Устранение неполадок

### whois команда не найдена
```bash
sudo apt install whois
```

### Порт занят
```bash
# Проверить занятые порты
sudo netstat -tlnp | grep :3000

# Изменить порт в коде или остановить процесс
```

### ngrok ошибки
```bash
# Проверить токен
echo $NGROK_AUTHTOKEN

# Ограничение на количество сессий - используйте один сервер
```

## 📞 Поддержка

Для вопросов и предложений:
- Проверьте логи сервера в консоли
- Убедитесь, что whois установлен
- Проверьте доступность портов
- Используйте curl для тестирования API

---

**🎉 Готово! Теперь у вас есть полнофункциональный WHOIS MCP сервер с красивым веб-интерфейсом!**
