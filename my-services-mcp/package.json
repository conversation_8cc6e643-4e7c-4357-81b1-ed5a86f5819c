{"name": "@your-username/my-services-mcp", "version": "1.0.0", "description": "MCP сервер для ваших текстовых услуг", "main": "dist/index.js", "bin": {"my-services-mcp": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "prepublishOnly": "npm run build"}, "keywords": ["mcp", "model-context-protocol", "services", "ai"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*", "README.md"], "engines": {"node": ">=18.0.0"}}