#!/usr/bin/env node

import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Ваши услуги - замените на реальные данные
const services = {
  "web-development": {
    title: "Веб-разработка",
    description: "Создание современных веб-сайтов и приложений",
    price: "от 50,000 руб",
    duration: "2-4 недели",
    technologies: ["React", "Node.js", "TypeScript", "PostgreSQL"],
    examples: ["Корпоративный сайт", "Интернет-магазин", "CRM система"]
  },
  "mobile-development": {
    title: "Мобильная разработка", 
    description: "Разработка приложений для iOS и Android",
    price: "от 80,000 руб",
    duration: "3-6 недель",
    technologies: ["React Native", "Flutter", "Swift", "Kotlin"],
    examples: ["Мобильное приложение", "Игровое приложение", "Социальная сеть"]
  },
  "consulting": {
    title: "IT Консультации",
    description: "Техническое консультирование и архитектурные решения", 
    price: "от 5,000 руб/час",
    duration: "по договоренности",
    technologies: ["Системная архитектура", "DevOps", "Cloud"],
    examples: ["Аудит кода", "Выбор технологий", "Оптимизация"]
  },
  "design": {
    title: "UI/UX Дизайн",
    description: "Создание пользовательских интерфейсов и опыта",
    price: "от 30,000 руб", 
    duration: "1-3 недели",
    technologies: ["Figma", "Adobe XD", "Sketch", "Principle"],
    examples: ["Дизайн сайта", "Мобильный интерфейс", "Брендинг"]
  }
};

const contactInfo = {
  email: "<EMAIL>",
  phone: "+7 (999) 123-45-67", 
  telegram: "@your_username",
  website: "https://your-website.com"
};

// Создаем MCP сервер
const server = new McpServer({
  name: "My Services MCP",
  version: "1.0.0"
});

// 📚 РЕСУРСЫ

// Список всех услуг
server.resource(
  "services-catalog",
  "services://catalog", 
  async (uri) => ({
    contents: [{
      uri: uri.href,
      mimeType: "application/json",
      text: JSON.stringify({
        services: Object.entries(services).map(([id, service]) => ({
          id,
          title: service.title,
          price: service.price,
          duration: service.duration
        })),
        total: Object.keys(services).length,
        contact: contactInfo,
        updated: new Date().toISOString()
      }, null, 2)
    }]
  })
);

// Детали конкретной услуги
server.resource(
  "service-info",
  new ResourceTemplate("services://{serviceId}", { list: undefined }),
  async (uri, { serviceId }) => {
    const service = services[serviceId as keyof typeof services];
    if (!service) {
      throw new Error(`Услуга ${serviceId} не найдена`);
    }
    
    return {
      contents: [{
        uri: uri.href,
        mimeType: "application/json", 
        text: JSON.stringify({
          id: serviceId,
          ...service,
          contact: contactInfo,
          portfolio: `https://your-website.com/portfolio/${serviceId}`
        }, null, 2)
      }]
    };
  }
);

// Прайс-лист в текстовом формате
server.resource(
  "price-list",
  "services://prices",
  async (uri) => ({
    contents: [{
      uri: uri.href,
      mimeType: "text/plain",
      text: `ПРАЙС-ЛИСТ УСЛУГ

${Object.entries(services).map(([id, service]) => 
  `${service.title}
  💰 Цена: ${service.price}
  ⏱️ Срок: ${service.duration}
  🔧 Технологии: ${service.technologies.join(', ')}
  📋 Примеры: ${service.examples.join(', ')}
  
`).join('')}

📞 КОНТАКТЫ:
📧 Email: ${contactInfo.email}
📱 Телефон: ${contactInfo.phone}
💬 Telegram: ${contactInfo.telegram}
🌐 Сайт: ${contactInfo.website}

Обновлено: ${new Date().toLocaleDateString('ru-RU')}`
    }]
  })
);

// 🛠️ ИНСТРУМЕНТЫ

// Поиск услуг
server.tool(
  "search-services",
  {
    query: z.string().describe("Поисковый запрос (технология, тип услуги, ключевое слово)")
  },
  async ({ query }) => {
    const searchTerm = query.toLowerCase();
    const found = Object.entries(services).filter(([id, service]) =>
      service.title.toLowerCase().includes(searchTerm) ||
      service.description.toLowerCase().includes(searchTerm) ||
      service.technologies.some(tech => tech.toLowerCase().includes(searchTerm)) ||
      service.examples.some(example => example.toLowerCase().includes(searchTerm))
    );

    return {
      content: [{
        type: "text",
        text: found.length > 0 
          ? `🔍 Найдено услуг по запросу "${query}": ${found.length}\n\n${found.map(([id, service]) => 
              `• ${service.title}\n  ${service.description}\n  ${service.price} | ${service.duration}\n`
            ).join('\n')}`
          : `❌ По запросу "${query}" ничего не найдено`
      }]
    };
  }
);

// Расчет стоимости
server.tool(
  "estimate-cost",
  {
    serviceType: z.enum(["web-development", "mobile-development", "consulting", "design"]),
    complexity: z.enum(["simple", "medium", "complex"]).describe("Простой/Средний/Сложный"),
    timeline: z.number().min(1).max(24).describe("Желаемый срок в неделях"),
    features: z.array(z.string()).optional().describe("Дополнительные функции")
  },
  async ({ serviceType, complexity, timeline, features = [] }) => {
    const service = services[serviceType];
    const basePrice = parseInt(service.price.match(/\d+/)?.[0] || "30000");
    
    const multipliers = {
      simple: 1,
      medium: 1.8, 
      complex: 3.2
    };
    
    const urgencyMultiplier = timeline < 2 ? 2.0 : timeline < 4 ? 1.5 : 1.0;
    const featuresMultiplier = 1 + (features.length * 0.2);
    
    const totalCost = Math.round(
      basePrice * multipliers[complexity] * urgencyMultiplier * featuresMultiplier
    );
    
    return {
      content: [{
        type: "text",
        text: `💰 РАСЧЕТ СТОИМОСТИ

📋 Услуга: ${service.title}
🎯 Сложность: ${complexity}
⏰ Срок: ${timeline} недель
${features.length > 0 ? `✨ Доп. функции: ${features.join(', ')}\n` : ''}

💵 Базовая стоимость: ${basePrice.toLocaleString('ru-RU')} руб
📊 Коэффициент сложности: ×${multipliers[complexity]}
⚡ Коэффициент срочности: ×${urgencyMultiplier}
${features.length > 0 ? `🔧 Коэффициент функций: ×${featuresMultiplier.toFixed(1)}\n` : ''}

🎯 ИТОГОВАЯ СТОИМОСТЬ: ${totalCost.toLocaleString('ru-RU')} руб

📞 Для уточнения деталей:
📧 ${contactInfo.email}
📱 ${contactInfo.phone}`
      }]
    };
  }
);

// Создание предложения
server.tool(
  "create-proposal", 
  {
    clientName: z.string().describe("Имя клиента"),
    serviceType: z.enum(["web-development", "mobile-development", "consulting", "design"]),
    projectDescription: z.string().describe("Описание проекта"),
    budget: z.string().optional().describe("Бюджет клиента")
  },
  async ({ clientName, serviceType, projectDescription, budget }) => {
    const service = services[serviceType];
    const date = new Date().toLocaleDateString('ru-RU');
    
    return {
      content: [{
        type: "text", 
        text: `📄 КОММЕРЧЕСКОЕ ПРЕДЛОЖЕНИЕ

📅 Дата: ${date}
👤 Клиент: ${clientName}
${budget ? `💰 Бюджет: ${budget}\n` : ''}

📝 ОПИСАНИЕ ПРОЕКТА:
${projectDescription}

🎯 ПРЕДЛАГАЕМОЕ РЕШЕНИЕ:
${service.title}
${service.description}

🔧 ТЕХНОЛОГИИ:
${service.technologies.join(' • ')}

💰 СТОИМОСТЬ: ${service.price}
⏱️ СРОК: ${service.duration}

📋 ПРИМЕРЫ НАШИХ РАБОТ:
${service.examples.map(ex => `• ${ex}`).join('\n')}

📞 КОНТАКТЫ:
📧 ${contactInfo.email}
📱 ${contactInfo.phone}  
💬 ${contactInfo.telegram}
🌐 ${contactInfo.website}

С уважением,
Команда разработки ✨`
      }]
    };
  }
);

// 💬 ПРОМПТЫ

server.prompt(
  "consultation-response",
  {
    clientName: z.string(),
    inquiry: z.string().describe("Запрос клиента"),
    serviceType: z.string().optional()
  },
  async ({ clientName, inquiry, serviceType }) => ({
    messages: [{
      role: "user",
      content: {
        type: "text",
        text: `Создай профессиональный ответ на запрос клиента:

Клиент: ${clientName}
Запрос: ${inquiry}
${serviceType ? `Интересующая услуга: ${serviceType}` : ''}

Используй информацию из каталога услуг и создай персонализированный ответ, включающий:
1. Приветствие и благодарность
2. Анализ потребностей
3. Рекомендации по услугам
4. Следующие шаги
5. Контактную информацию

Тон: профессиональный, экспертный, дружелюбный`
      }
    }]
  })
);

// Запуск сервера
async function main() {
  console.log("🚀 My Services MCP Server запущен");
  console.log(`📋 Доступно услуг: ${Object.keys(services).length}`);
  console.log("📞 Контакт:", contactInfo.email);
  
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
