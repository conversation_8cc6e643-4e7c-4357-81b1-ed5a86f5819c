# 🚀 MCP Services Server - Полная экосистема

Полнофункциональная система MCP (Model Context Protocol) серверов для предоставления информации об услугах AI ассистентам с веб-интерфейсом управления и постоянным сохранением данных.

## 📁 Структура проекта

```
/home/<USER>/My/111/
├── 📋 README.md                          # Этот файл
├── 📦 package.json                       # Зависимости проекта
├── 📚 node_modules/                      # Установленные пакеты
├── 🔧 typescript-sdk/                    # MCP TypeScript SDK
│
├── 🏠 ЛОКАЛЬНЫЕ MCP СЕРВЕРЫ:
│   ├── hybrid-mcp-server.ts             # Гибридный сервер (STDIO + HTTP мониторинг)
│   └── my-services-server-simple.ts     # Простой STDIO сервер
│
├── 🌐 УДАЛЕННЫЕ MCP СЕРВЕРЫ:
│   ├── sse-mcp-server.js                # SSE сервер (без ngrok)
│   ├── ngrok-server.js                  # SSE сервер с ngrok
│   └── http-mcp-server.ts               # HTTP Streamable сервер
│
├── 💬 ВЕБ-ИНТЕРФЕЙСЫ:
│   ├── simple-mistral-chat.html         # Чат с Mistral AI + MCP
│   ├── add-service.html                 # Веб-форма добавления услуг (Bootstrap)
│   └── mcp-web-client.html              # Тестовый MCP клиент
│
├── 🔧 УТИЛИТЫ:
│   ├── simple-mcp-client.js             # Консольный MCP клиент
│   └── test-my-services.ts              # Тестирование локального сервера
│
├── 💾 ДАННЫЕ:
│   └── services-data.json               # Постоянное хранение услуг (автосоздается)
│
└── ⚙️ КОНФИГУРАЦИИ:
    ├── final-mcp-config.json            # Конфигурация для Augment (STDIO)
    ├── http-mcp-config.json             # Конфигурация для HTTP клиента
    └── vscode-mcp-config.json           # Конфигурация для VS Code
```

## 🎯 Основные компоненты

### 1. 🏠 Локальные MCP серверы (STDIO)

#### `hybrid-mcp-server.ts` - РЕКОМЕНДУЕМЫЙ
**Назначение:** Универсальный сервер с двумя режимами работы
- **STDIO режим:** для подключения к VS Code/Augment
- **HTTP режим:** для веб-мониторинга

**Запуск:**
```bash
# STDIO режим (для MCP клиентов)
node --import ./typescript-sdk/node_modules/tsx/dist/esm/index.mjs hybrid-mcp-server.ts

# HTTP режим (для мониторинга)
node --import ./typescript-sdk/node_modules/tsx/dist/esm/index.mjs hybrid-mcp-server.ts http
```

**Конфигурация для Augment:**
```json
{
  "mcpServers": {
    "my_services": {
      "command": "node",
      "args": [
        "--import",
        "/home/<USER>/My/111/typescript-sdk/node_modules/tsx/dist/esm/index.mjs",
        "/home/<USER>/My/111/hybrid-mcp-server.ts"
      ],
      "cwd": "/home/<USER>/My/111"
    }
  }
}
```

### 2. 🌐 Удаленные MCP серверы (HTTP/SSE)

#### `ngrok-server.js` - ДЛЯ УДАЛЕННОГО ДОСТУПА + ВЕБ-ИНТЕРФЕЙС
**Назначение:** HTTP сервер с ngrok для доступа из интернета + веб-интерфейс управления услугами

**Запуск:**
```bash
NGROK_AUTHTOKEN=************************************************* node ngrok-server.js
```

**Получите публичный URL:** `https://xxx-xxx-xxx.ngrok-free.app`

**🌟 Новые возможности:**
- **💬 Mistral AI чат:** `/chat` - чат с AI, подключенный к MCP
- **➕ Добавление услуг:** `/add-service` - Bootstrap форма для добавления услуг
- **� WHOIS проверка:** `/whois` - проверка доменов через whois с красивым интерфейсом
- **�💾 Постоянное сохранение:** услуги автоматически сохраняются в `services-data.json`
- **🔄 Автозагрузка:** при перезапуске услуги загружаются из файла

### 3. 💬 Веб-интерфейсы

#### `simple-mistral-chat.html` - ГОТОВЫЙ ЧАТ С AI
**Назначение:** Веб-чат с Mistral AI и подключением к MCP серверу

**Способы запуска:**
1. **Через ngrok сервер:** `http://localhost:3000/chat` (рекомендуется)
2. **Через HTTP сервер:** `python3 -m http.server 8080` → `http://localhost:8080/simple-mistral-chat.html`
3. **Прямо из файла:** `file:///path/to/simple-mistral-chat.html`

**API ключ Mistral:** `uEvRJbFSHPahqcULTbqMH3NQ0t9ASn71`

#### `add-service.html` - УПРАВЛЕНИЕ УСЛУГАМИ
**Назначение:** Bootstrap веб-форма для добавления новых услуг

**Доступ:**
- **Локально:** `http://localhost:3000/add-service`
- **Публично:** `https://your-ngrok-url.ngrok-free.app/add-service`

**Возможности:**
- ✅ Добавление новых услуг через удобную форму
- ✅ Валидация полей и проверка уникальности ID
- ✅ Динамическое добавление технологий
- ✅ Отображение существующих услуг
- ✅ Автоматическое сохранение в файл

## 🛠️ Управление услугами

### 💾 Постоянное сохранение
Все услуги автоматически сохраняются в файл `services-data.json` и загружаются при перезапуске сервера.

### 📋 Дефолтные услуги
При первом запуске создаются следующие услуги:

```javascript
const services = {
  "web-development": {
    title: "Веб-разработка",
    price: "от 50,000 руб",
    duration: "2-4 недели",
    technologies: ["React", "Node.js", "TypeScript", "PostgreSQL"]
  },
  "mobile-development": {
    title: "Мобильная разработка",
    price: "от 80,000 руб", 
    duration: "3-6 недель",
    technologies: ["React Native", "Flutter", "Swift", "Kotlin"]
  },
  "consulting": {
    title: "IT Консультации",
    price: "от 5,000 руб/час",
    duration: "по договоренности",
    technologies: ["Системная архитектура", "DevOps", "Cloud"]
  },
  "testing": {
    title: "Тестовая услуга",
    price: "от 200,000 руб",
    duration: "1-3 месяца", 
    technologies: ["Jest", "Cypress", "Selenium", "Postman"]
  }
};
```

### 🔧 API для управления услугами

#### Получение всех услуг
```bash
GET /api/services
```

#### Добавление новой услуги
```bash
POST /api/services
Content-Type: application/json

{
  "id": "unique-service-id",
  "title": "Название услуги",
  "description": "Описание услуги",
  "price": "от 50,000 руб",
  "duration": "2-4 недели",
  "technologies": ["React", "Node.js"]
}
```

#### Удаление услуги
```bash
DELETE /api/services/:id
```

**Примеры использования:**
```bash
# Получить все услуги
curl http://localhost:3000/api/services

# Добавить новую услуг
curl -X POST http://localhost:3000/api/services \
  -H "Content-Type: application/json" \
  -d '{"id": "ai-development", "title": "AI Разработка", "description": "Создание AI решений", "price": "от 100,000 руб", "duration": "4-8 недель", "technologies": ["Python", "TensorFlow"]}'

# Удалить услугу
curl -X DELETE http://localhost:3000/api/services/ai-development
```

## 🔧 MCP Возможности

### Ресурсы (Resources):
- `services://catalog` - каталог всех услуг
- `services://web-development` - детали веб-разработки
- `services://mobile-development` - детали мобильной разработки
- `services://consulting` - детали консультаций
- `services://testing` - детали тестовой услуги

### Инструменты (Tools):
- `search-services` - поиск услуг по ключевому слову
- `estimate-cost` - расчет стоимости проекта
- `check-domain-whois` - проверка информации о домене через whois

### Промпты (Prompts):
- `consultation-response` - шаблон ответа на запрос клиента

## 🚀 Быстрый старт

### 1. Локальное использование (VS Code/Augment):
```bash
# Запустите локальный сервер
node --import ./typescript-sdk/node_modules/tsx/dist/esm/index.mjs hybrid-mcp-server.ts

# Добавьте конфигурацию в Augment (см. выше)
# Используйте: use my_services
```

### 2. Удаленное использование с веб-интерфейсом (РЕКОМЕНДУЕТСЯ):
```bash
# Запустите ngrok сервер с веб-интерфейсом
NGROK_AUTHTOKEN=************************************************* node ngrok-server.js

# Откройте веб-интерфейсы:
# 🏠 Главная страница: http://localhost:3000
# 💬 AI чат: http://localhost:3000/chat
# ➕ Добавление услуг: http://localhost:3000/add-service
# � WHOIS проверка: http://localhost:3000/whois
# �📊 API услуг: http://localhost:3000/api/services

# Публичные URL (замените на ваш ngrok URL):
# https://your-ngrok-url.ngrok-free.app/chat
# https://your-ngrok-url.ngrok-free.app/add-service
# https://your-ngrok-url.ngrok-free.app/whois
```

### 3. Альтернативные способы запуска чата:
```bash
# Через Python HTTP сервер
python3 -m http.server 8080
# Затем: http://localhost:8080/simple-mistral-chat.html

# Прямо из файла (могут быть CORS ограничения)
open simple-mistral-chat.html
```

## 🧪 Тестирование

### Консольный тест:
```bash
node simple-mcp-client.js test
```

### Веб-тест:
Откройте `mcp-web-client.html` в браузере

### Проверка статуса удаленного сервера:
```bash
curl https://your-ngrok-url.ngrok-free.app/stats
```

## 📝 Редактирование услуг

### 🌟 Новый способ (РЕКОМЕНДУЕТСЯ):
**Через веб-интерфейс:** `http://localhost:3000/add-service`
- ✅ Удобная Bootstrap форма
- ✅ Автоматическое сохранение в файл
- ✅ Валидация данных
- ✅ Сохраняется при перезапуске

### 🔧 Через API:
```bash
# Добавить услугу
curl -X POST http://localhost:3000/api/services -H "Content-Type: application/json" -d '{...}'

# Удалить услугу
curl -X DELETE http://localhost:3000/api/services/service-id
```

### 📝 Ручное редактирование:
**Файл данных:** `services-data.json` (создается автоматически)

**Альтернативно в коде серверов:**
1. **Локальные серверы:** `hybrid-mcp-server.ts`, `my-services-server-simple.ts`
2. **Удаленные серверы:** `ngrok-server.js`, `sse-mcp-server.js`

## 🌐 Развертывание

### GitHub Pages:
1. Создайте репозиторий
2. Загрузите HTML файлы
3. Включите GitHub Pages

### Vercel:
```bash
vercel --prod
```

### Netlify:
Перетащите HTML файлы на netlify.com

## 🔑 Конфигурации

### Полная конфигурация с Context7 и WHOIS:
```json
{
  "mcpServers": {
    "context7-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@upstash/context7-mcp",
        "--key",
        "d598d734-db1b-4af8-9b70-b2c1d9bbd988"
      ]
    },
    "services_with_whois": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "https://your-ngrok-url.ngrok-free.app/mcp"
      ]
    },
    "whois_local": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "http://localhost:3001/mcp"
      ]
    }
  }
}
```

### Для VS Code/Augment:
Используйте `mcp-config-with-whois.json` или `final-mcp-config.json`

### Для удаленных клиентов:
```json
{
  "mcpServers": {
    "remote_services": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "YOUR_NGROK_URL/mcp"]
    }
  }
}
```

## 🆘 Устранение неполадок

### Сервер не запускается:
```bash
# Проверьте зависимости
npm install

# Проверьте TypeScript SDK
cd typescript-sdk && npm install && npm run build
```

### MCP клиент не подключается:
1. Убедитесь, что сервер запущен
2. Проверьте правильность путей в конфигурации
3. Перезапустите VS Code после изменения конфигурации

### Mistral API не работает:
1. Проверьте API ключ
2. Убедитесь в доступности api.mistral.ai
3. Проверьте консоль браузера на ошибки CORS

## 📞 Контакты

Для изменения контактной информации отредактируйте объект `contactInfo` в серверах:

```javascript
const contactInfo = {
  email: "<EMAIL>",
  phone: "+7 (999) 123-45-67", 
  telegram: "@your_username"
};
```

## 🌟 Новые возможности v2.0

### 💾 Постоянное сохранение данных
- **Автосохранение:** Все услуги автоматически сохраняются в `services-data.json`
- **Автозагрузка:** При перезапуске сервера услуги загружаются из файла
- **Резервное копирование:** Легко создавать бэкапы файла данных

### 🎨 Современный веб-интерфейс
- **Bootstrap дизайн:** Красивый и адаптивный интерфейс
- **Форма добавления услуг:** Удобное добавление через веб-форму
- **Валидация:** Проверка данных на клиенте и сервере
- **Динамические технологии:** Добавление тегов технологий

### 🤖 Интеграция с AI
- **Mistral AI чат:** Готовый чат с подключением к MCP
- **Автоматическое обновление URL:** Чат автоматически подключается к актуальному ngrok URL
- **Быстрые команды:** Готовые кнопки для популярных запросов

### 🔧 REST API
- **GET /api/services** - получение всех услуг
- **POST /api/services** - добавление новой услуги
- **DELETE /api/services/:id** - удаление услуги
- **Автоматическое сохранение** при всех операциях

### 🌐 Множественные способы доступа
- **Локальный доступ:** `http://localhost:3000`
- **Публичный доступ:** через ngrok URL
- **Файловый доступ:** прямое открытие HTML файлов
- **HTTP сервер:** через Python или другие серверы

---

**🎉 Готово! Теперь у вас есть полная экосистема MCP для ваших услуг с современным веб-интерфейсом и постоянным сохранением данных!**
