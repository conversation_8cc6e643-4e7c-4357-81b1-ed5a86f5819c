<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 WHOIS Проверка доменов</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .result-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: block;
        }
        .domain-info {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        .error-info {
            background: #f8d7da;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        .raw-output {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .quick-domains {
            margin-top: 15px;
        }
        .quick-domain-btn {
            margin: 2px;
            font-size: 12px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-search"></i> WHOIS Проверка доменов
                        </h2>
                        <p class="mb-0 mt-2">Получите подробную информацию о любом домене</p>
                    </div>
                    <div class="card-body">
                        <form id="whoisForm">
                            <div class="mb-3">
                                <label for="domainInput" class="form-label">
                                    <i class="fas fa-globe"></i> Доменное имя
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="domainInput" 
                                       placeholder="Введите домен (например: example.com)"
                                       required>
                                <div class="form-text">
                                    Введите домен без протокола (http/https) и www
                                </div>
                            </div>
                            
                            <div class="quick-domains">
                                <small class="text-muted">Быстрая проверка:</small><br>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-domain-btn" data-domain="google.com">google.com</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-domain-btn" data-domain="yandex.ru">yandex.ru</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-domain-btn" data-domain="github.com">github.com</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-domain-btn" data-domain="stackoverflow.com">stackoverflow.com</button>
                            </div>
                            
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Проверить домен
                                </button>
                            </div>
                        </form>
                        
                        <div id="loading" class="loading text-center mt-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Загрузка...</span>
                            </div>
                            <p class="mt-2">Выполняется WHOIS запрос...</p>
                        </div>
                        
                        <div id="results"></div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-white">
                        <i class="fas fa-server"></i> Powered by WHOIS MCP Server
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Определяем базовый URL для API
        const API_BASE = window.location.origin;
        
        // Элементы DOM
        const form = document.getElementById('whoisForm');
        const domainInput = document.getElementById('domainInput');
        const loading = document.getElementById('loading');
        const results = document.getElementById('results');
        const quickDomainBtns = document.querySelectorAll('.quick-domain-btn');
        
        // Обработчики быстрых доменов
        quickDomainBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const domain = btn.getAttribute('data-domain');
                domainInput.value = domain;
                checkDomain(domain);
            });
        });
        
        // Обработчик формы
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const domain = domainInput.value.trim();
            if (domain) {
                checkDomain(domain);
            }
        });
        
        // Функция проверки домена
        async function checkDomain(domain) {
            // Очистка предыдущих результатов
            results.innerHTML = '';
            loading.classList.add('show');
            
            try {
                console.log(`Проверяем домен: ${domain}`);
                const response = await fetch(`${API_BASE}/api/whois/${encodeURIComponent(domain)}`);
                const data = await response.json();
                
                loading.classList.remove('show');
                
                if (data.success) {
                    displayResults(data.data);
                } else {
                    displayError(data.error);
                }
            } catch (error) {
                loading.classList.remove('show');
                displayError(`Ошибка сети: ${error.message}`);
            }
        }
        
        // Отображение результатов
        function displayResults(data) {
            const parsed = data.parsed;
            
            results.innerHTML = `
                <div class="result-container">
                    <h5><i class="fas fa-info-circle"></i> Информация о домене: ${data.domain}</h5>
                    
                    <div class="domain-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-calendar-plus"></i> Дата создания:</strong><br>
                                <span class="text-muted">${parsed.creationDate || 'Не указана'}</span>
                            </div>
                            <div class="col-md-6">
                                <strong><i class="fas fa-calendar-times"></i> Дата истечения:</strong><br>
                                <span class="text-muted">${parsed.expiryDate || 'Не указана'}</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-building"></i> Регистратор:</strong><br>
                                <span class="text-muted">${parsed.registrar || 'Не указан'}</span>
                            </div>
                            <div class="col-md-6">
                                <strong><i class="fas fa-shield-alt"></i> DNSSEC:</strong><br>
                                <span class="text-muted">${parsed.dnssec || 'Не указан'}</span>
                            </div>
                        </div>
                        
                        ${parsed.nameServers && parsed.nameServers.length > 0 ? `
                        <hr>
                        <strong><i class="fas fa-server"></i> DNS серверы:</strong><br>
                        <ul class="list-unstyled mt-2">
                            ${parsed.nameServers.map(ns => `<li><code>${ns}</code></li>`).join('')}
                        </ul>
                        ` : ''}
                        
                        ${parsed.domainStatus && parsed.domainStatus.length > 0 ? `
                        <hr>
                        <strong><i class="fas fa-flag"></i> Статус домена:</strong><br>
                        <ul class="list-unstyled mt-2">
                            ${parsed.domainStatus.map(status => `<li><span class="badge bg-info">${status}</span></li>`).join('')}
                        </ul>
                        ` : ''}
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#rawOutput">
                            <i class="fas fa-code"></i> Показать полный WHOIS вывод
                        </button>
                    </div>
                    
                    <div class="collapse mt-3" id="rawOutput">
                        <div class="raw-output">${data.raw}</div>
                    </div>
                    
                    <small class="text-muted mt-3 d-block">
                        <i class="fas fa-clock"></i> Проверено: ${new Date(data.timestamp).toLocaleString('ru-RU')}
                    </small>
                </div>
            `;
        }
        
        // Отображение ошибки
        function displayError(error) {
            results.innerHTML = `
                <div class="error-info">
                    <h6><i class="fas fa-exclamation-triangle"></i> Ошибка</h6>
                    <p class="mb-0">${error}</p>
                    <small class="text-muted">
                        Убедитесь, что домен введен корректно и доступен для WHOIS запросов.
                    </small>
                </div>
            `;
        }
        
        // Автофокус на поле ввода
        domainInput.focus();
    </script>
</body>
</html>
