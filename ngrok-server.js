#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const ngrok = require('@ngrok/ngrok');
const { randomUUID } = require('crypto');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Файл для сохранения услуг
const SERVICES_FILE = path.join(__dirname, 'services-data.json');

// Функция загрузки услуг из файла
function loadServices() {
  try {
    if (fs.existsSync(SERVICES_FILE)) {
      const data = fs.readFileSync(SERVICES_FILE, 'utf8');
      const loadedServices = JSON.parse(data);
      console.log(`📂 [${new Date().toLocaleTimeString()}] Загружено услуг из файла: ${Object.keys(loadedServices).length}`);
      return loadedServices;
    }
  } catch (error) {
    console.error('❌ Ошибка загрузки услуг из файла:', error);
  }

  // Возвращаем дефолтные услуги, если файл не найден или поврежден
  console.log(`📋 [${new Date().toLocaleTimeString()}] Используются дефолтные услуги`);
  return getDefaultServices();
}

// Функция сохранения услуг в файл
function saveServices(services) {
  try {
    fs.writeFileSync(SERVICES_FILE, JSON.stringify(services, null, 2), 'utf8');
    console.log(`💾 [${new Date().toLocaleTimeString()}] Услуги сохранены в файл: ${Object.keys(services).length} услуг`);
  } catch (error) {
    console.error('❌ Ошибка сохранения услуг в файл:', error);
  }
}

// Дефолтные услуги
function getDefaultServices() {
  return {
    "web-development": {
      title: "Веб-разработка",
      description: "Создание современных веб-сайтов и приложений",
      price: "от 50,000 руб",
      duration: "2-4 недели",
      technologies: ["React", "Node.js", "TypeScript", "PostgreSQL"]
    },
    "mobile-development": {
      title: "Мобильная разработка",
      description: "Разработка приложений для iOS и Android",
      price: "от 80,000 руб",
      duration: "3-6 недель",
      technologies: ["React Native", "Flutter", "Swift", "Kotlin"]
    },
    "consulting": {
      title: "IT Консультации",
      description: "Техническое консультирование и архитектурные решения",
      price: "от 5,000 руб/час",
      duration: "по договоренности",
      technologies: ["Системная архитектура", "DevOps", "Cloud"]
    },
    "testing": {
      title: "Тестовая услуга",
      description: "Тестирование и QA для ваших проектов",
      price: "от 200,000 руб",
      duration: "1-3 месяца",
      technologies: ["Jest", "Cypress", "Selenium", "Postman"]
    }
  };
}

// Загружаем услуги при старте
let services = loadServices();

const contactInfo = {
  email: "<EMAIL>",
  phone: "+7 (999) 123-45-67",
  telegram: "@your_username"
};

// Функция выполнения whois запроса
function executeWhois(domain) {
  return new Promise((resolve, reject) => {
    console.log(`🔍 Выполняем whois для домена: ${domain}`);

    // Проверяем различные пути для whois
    const whoisPaths = ['/usr/bin/whois', '/bin/whois', 'whois'];
    let whoisProcess = null;

    for (const whoisPath of whoisPaths) {
      try {
        whoisProcess = spawn(whoisPath, [domain], {
          stdio: ['pipe', 'pipe', 'pipe']
        });
        break;
      } catch (error) {
        continue;
      }
    }

    if (!whoisProcess) {
      reject(new Error('whois команда не найдена. Установите: sudo apt install whois'));
      return;
    }

    let output = '';
    let errorOutput = '';

    whoisProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    whoisProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    whoisProcess.on('close', (code) => {
      if (code === 0) {
        resolve(output);
      } else {
        reject(new Error(`whois завершился с кодом ${code}: ${errorOutput}`));
      }
    });

    whoisProcess.on('error', (error) => {
      reject(new Error(`Ошибка выполнения whois: ${error.message}`));
    });

    // Таймаут 30 секунд
    setTimeout(() => {
      whoisProcess.kill();
      reject(new Error('Таймаут whois запроса (30 сек)'));
    }, 30000);
  });
}

// Парсинг whois результата
function parseWhoisResult(whoisOutput, domain) {
  const lines = whoisOutput.split('\n');
  const result = {
    domain: domain,
    timestamp: new Date().toISOString(),
    raw: whoisOutput,
    parsed: {}
  };

  // Основные поля для парсинга
  const fields = {
    'Domain Name': 'domainName',
    'Registry Domain ID': 'registryDomainId',
    'Registrar WHOIS Server': 'registrarWhoisServer',
    'Registrar URL': 'registrarUrl',
    'Updated Date': 'updatedDate',
    'Creation Date': 'creationDate',
    'Registry Expiry Date': 'expiryDate',
    'Registrar': 'registrar',
    'Registrar IANA ID': 'registrarIanaId',
    'Registrar Abuse Contact Email': 'abuseEmail',
    'Registrar Abuse Contact Phone': 'abusePhone',
    'Domain Status': 'domainStatus',
    'Name Server': 'nameServers',
    'DNSSEC': 'dnssec'
  };

  result.parsed.nameServers = [];
  result.parsed.domainStatus = [];

  lines.forEach(line => {
    const trimmedLine = line.trim();
    if (!trimmedLine || trimmedLine.startsWith('%') || trimmedLine.startsWith('>>>')) {
      return;
    }

    for (const [whoisField, resultField] of Object.entries(fields)) {
      if (trimmedLine.toLowerCase().startsWith(whoisField.toLowerCase() + ':')) {
        const value = trimmedLine.substring(whoisField.length + 1).trim();

        if (resultField === 'nameServers') {
          result.parsed.nameServers.push(value);
        } else if (resultField === 'domainStatus') {
          result.parsed.domainStatus.push(value);
        } else {
          result.parsed[resultField] = value;
        }
      }
    }
  });

  return result;
}

const app = express();
app.use(express.json());
app.use(cors({
  origin: true,
  credentials: true
}));

const sseConnections = new Map();
let ngrokUrl = '';

// Главная страница с ngrok URL
app.get('/', (req, res) => {
  res.send(`
    <h1>🌐 SSE MCP Server через ngrok</h1>
    <p><strong>Локальный URL:</strong> http://localhost:3000</p>
    <p><strong>🌍 Публичный URL:</strong> <a href="${ngrokUrl}" target="_blank">${ngrokUrl}</a></p>
    <p><strong>Время работы:</strong> ${Math.round(process.uptime())} секунд</p>
    <p><strong>SSE соединений:</strong> ${sseConnections.size}</p>
    
    <h2>🛠️ Доступные услуги:</h2>
    <ul>
      ${Object.entries(services).map(([id, service]) => 
        `<li><strong>${service.title}</strong> - ${service.price}</li>`
      ).join('')}
    </ul>
    
    <h2>📡 Конфигурация для удаленного Augment:</h2>
    <pre>{
  "mcpServers": {
    "remote_services": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "${ngrokUrl}/mcp"]
    }
  }
}</pre>

    <h2>🧪 Тестирование:</h2>
    <p><a href="/test">Локальный тест</a></p>
    <p><a href="${ngrokUrl}/test" target="_blank">Удаленный тест</a></p>

    <h2>💬 Mistral AI Чат:</h2>
    <p><a href="/chat" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">🤖 Открыть чат с AI</a></p>
    <p><a href="${ngrokUrl}/chat" target="_blank">Чат (публичный URL)</a></p>

    <h2>➕ Управление услугами:</h2>
    <p><a href="/add-service" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">Добавить новую услугу</a></p>
    <p><a href="${ngrokUrl}/add-service" target="_blank">Добавить услугу (публичный URL)</a></p>

    <h2>🔍 WHOIS проверка доменов:</h2>
    <p><a href="/whois" style="background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">🔍 Проверить домен</a></p>
    <p><a href="${ngrokUrl}/whois" target="_blank">WHOIS проверка (публичный URL)</a></p>

    <h2>📊 API:</h2>
    <ul>
      <li><a href="/stats">Статистика</a></li>
      <li><a href="/mcp">SSE поток</a></li>
      <li><a href="/api/services">Список услуг (JSON)</a></li>
      <li><a href="/api/whois/example.com">WHOIS проверка (example.com)</a></li>
      <li>POST /mcp - MCP JSON-RPC endpoint</li>
      <li>POST /api/services - Добавление услуги</li>
      <li>GET /api/whois/:domain - WHOIS проверка домена</li>
    </ul>
  `);
});

// SSE endpoint
app.get('/mcp', (req, res) => {
  console.log(`📡 [${new Date().toLocaleTimeString()}] Новое SSE соединение от ${req.ip}`);
  
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  const connectionId = randomUUID();
  sseConnections.set(connectionId, { res, ip: req.ip, connected: Date.now() });

  // Отправляем endpoint
  const postEndpoint = ngrokUrl ? `${ngrokUrl}/mcp` : `${req.protocol}://${req.get('host')}/mcp`;
  res.write(`event: endpoint\n`);
  res.write(`data: ${JSON.stringify({ uri: postEndpoint })}\n\n`);

  req.on('close', () => {
    console.log(`🔌 [${new Date().toLocaleTimeString()}] SSE соединение закрыто (${req.ip})`);
    sseConnections.delete(connectionId);
  });

  const keepAlive = setInterval(() => {
    if (sseConnections.has(connectionId)) {
      res.write(`event: ping\n`);
      res.write(`data: ${JSON.stringify({ timestamp: Date.now(), server: 'ngrok' })}\n\n`);
    } else {
      clearInterval(keepAlive);
    }
  }, 30000);
});

// MCP JSON-RPC endpoint
app.post('/mcp', async (req, res) => {
  const clientIp = req.ip;
  console.log(`📨 [${new Date().toLocaleTimeString()}] MCP запрос от ${clientIp}:`, req.body?.method || 'unknown');
  
  try {
    const request = req.body;
    let response;

    switch (request.method) {
      case 'initialize':
        response = {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            protocolVersion: '2024-11-05',
            capabilities: {
              resources: { listChanged: true },
              tools: { listChanged: true }
            },
            serverInfo: {
              name: 'ngrok SSE Services MCP',
              version: '1.0.0',
              publicUrl: ngrokUrl
            }
          }
        };
        break;

      case 'resources/list':
        response = {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            resources: [
              {
                uri: 'services://catalog',
                name: 'Services Catalog',
                description: 'Каталог всех доступных услуг через ngrok',
                mimeType: 'application/json'
              },
              ...Object.keys(services).map(id => ({
                uri: `services://${id}`,
                name: services[id].title,
                description: services[id].description,
                mimeType: 'application/json'
              }))
            ]
          }
        };
        break;

      case 'resources/read':
        const uri = request.params.uri;
        if (uri === 'services://catalog') {
          response = {
            jsonrpc: '2.0',
            id: request.id,
            result: {
              contents: [{
                uri: uri,
                mimeType: 'application/json',
                text: JSON.stringify({
                  services: Object.entries(services).map(([id, service]) => ({
                    id,
                    title: service.title,
                    price: service.price,
                    duration: service.duration
                  })),
                  contact: contactInfo,
                  serverInfo: {
                    type: 'ngrok SSE MCP Server',
                    publicUrl: ngrokUrl,
                    localUrl: 'http://localhost:3000',
                    activeConnections: sseConnections.size
                  },
                  timestamp: new Date().toISOString()
                }, null, 2)
              }]
            }
          };
        } else {
          const serviceId = uri.replace('services://', '');
          const service = services[serviceId];
          if (service) {
            response = {
              jsonrpc: '2.0',
              id: request.id,
              result: {
                contents: [{
                  uri: uri,
                  mimeType: 'application/json',
                  text: JSON.stringify({
                    id: serviceId,
                    ...service,
                    contact: contactInfo,
                    accessedVia: 'ngrok',
                    publicUrl: ngrokUrl
                  }, null, 2)
                }]
              }
            };
          } else {
            response = {
              jsonrpc: '2.0',
              id: request.id,
              error: { code: -32602, message: `Resource not found: ${uri}` }
            };
          }
        }
        break;

      case 'tools/list':
        response = {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            tools: [
              {
                name: 'search-services',
                description: 'Поиск услуг через ngrok',
                inputSchema: {
                  type: 'object',
                  properties: {
                    query: { type: 'string', description: 'Поисковый запрос' }
                  },
                  required: ['query']
                }
              },
              {
                name: 'estimate-cost',
                description: 'Расчет стоимости через ngrok',
                inputSchema: {
                  type: 'object',
                  properties: {
                    serviceType: {
                      type: 'string',
                      enum: Object.keys(services),
                      description: 'Тип услуги'
                    },
                    complexity: {
                      type: 'string',
                      enum: ['simple', 'medium', 'complex'],
                      description: 'Сложность проекта'
                    },
                    timeline: {
                      type: 'number',
                      minimum: 1,
                      maximum: 24,
                      description: 'Срок в неделях'
                    }
                  },
                  required: ['serviceType', 'complexity', 'timeline']
                }
              },
              {
                name: 'check-domain-whois',
                description: 'Проверка информации о домене через whois',
                inputSchema: {
                  type: 'object',
                  properties: {
                    domain: {
                      type: 'string',
                      description: 'Доменное имя для проверки (например: example.com)'
                    }
                  },
                  required: ['domain']
                }
              }
            ]
          }
        };
        break;

      case 'tools/call':
        const toolName = request.params.name;
        const args = request.params.arguments;

        if (toolName === 'search-services') {
          const query = args.query.toLowerCase();
          const found = Object.entries(services).filter(([id, service]) =>
            service.title.toLowerCase().includes(query) ||
            service.description.toLowerCase().includes(query) ||
            service.technologies.some(tech => tech.toLowerCase().includes(query))
          );

          response = {
            jsonrpc: '2.0',
            id: request.id,
            result: {
              content: [{
                type: 'text',
                text: found.length > 0 
                  ? `🌍 ngrok сервер нашел по запросу "${args.query}":\n\n${found.map(([id, service]) => 
                      `• ${service.title} - ${service.price} (${service.duration})`
                    ).join('\n')}\n\n📞 Контакт: ${contactInfo.email}\n🌐 Публичный URL: ${ngrokUrl}\n📡 SSE соединений: ${sseConnections.size}\n👤 Запрос от: ${clientIp}`
                  : `❌ По запросу "${args.query}" ничего не найдено`
              }]
            }
          };
        } else if (toolName === 'estimate-cost') {
          const service = services[args.serviceType];
          const basePrice = parseInt(service.price.match(/\d+/)?.[0] || "30000");
          
          const multipliers = { simple: 1, medium: 1.8, complex: 3.2 };
          const urgencyMultiplier = args.timeline < 2 ? 2.0 : args.timeline < 4 ? 1.5 : 1.0;
          
          const totalCost = Math.round(basePrice * multipliers[args.complexity] * urgencyMultiplier);
          
          response = {
            jsonrpc: '2.0',
            id: request.id,
            result: {
              content: [{
                type: 'text',
                text: `💰 РАСЧЕТ ОТ NGROK СЕРВЕРА

📋 ${service.title}
🎯 Сложность: ${args.complexity}
⏰ Срок: ${args.timeline} недель

💵 Базовая стоимость: ${basePrice.toLocaleString('ru-RU')} руб
📊 Коэффициент сложности: ×${multipliers[args.complexity]}
⚡ Коэффициент срочности: ×${urgencyMultiplier}

🎯 ИТОГО: ${totalCost.toLocaleString('ru-RU')} руб

📞 Контакт: ${contactInfo.email}
🌍 Публичный URL: ${ngrokUrl}
📡 SSE соединений: ${sseConnections.size}
👤 Запрос от: ${clientIp}
⏱️ Время работы: ${Math.round(process.uptime())}с`
              }]
            }
          };
        } else if (toolName === 'check-domain-whois') {
          try {
            const whoisResult = await executeWhois(args.domain);
            const parsedResult = parseWhoisResult(whoisResult, args.domain);

            response = {
              jsonrpc: '2.0',
              id: request.id,
              result: {
                content: [{
                  type: 'text',
                  text: `🔍 WHOIS информация для домена: ${args.domain}

📅 Дата создания: ${parsedResult.parsed.creationDate || 'Не указана'}
📅 Дата истечения: ${parsedResult.parsed.expiryDate || 'Не указана'}
🏢 Регистратор: ${parsedResult.parsed.registrar || 'Не указан'}
🌐 DNS серверы: ${parsedResult.parsed.nameServers.join(', ') || 'Не указаны'}
📊 Статус: ${parsedResult.parsed.domainStatus.join(', ') || 'Не указан'}

🌍 Запрос через ngrok: ${ngrokUrl}
👤 Запрос от: ${clientIp}
⏱️ Время проверки: ${new Date().toLocaleString('ru-RU')}

📋 Полная информация:
${whoisResult}`
                }]
              }
            };
          } catch (error) {
            response = {
              jsonrpc: '2.0',
              id: request.id,
              error: {
                code: -32000,
                message: `Ошибка whois запроса: ${error.message}`
              }
            };
          }
        } else {
          response = {
            jsonrpc: '2.0',
            id: request.id,
            error: { code: -32601, message: `Unknown tool: ${toolName}` }
          };
        }
        break;

      default:
        response = {
          jsonrpc: '2.0',
          id: request.id,
          error: { code: -32601, message: 'Method not found' }
        };
    }

    res.json(response);

    // SSE уведомление
    const notification = {
      method: 'notifications/message',
      params: {
        level: 'info',
        message: `ngrok: ${request.method} от ${clientIp}`,
        timestamp: new Date().toISOString()
      }
    };

    sseConnections.forEach(({ res: connection }) => {
      try {
        connection.write(`event: message\n`);
        connection.write(`data: ${JSON.stringify(notification)}\n\n`);
      } catch (error) {
        console.error('Ошибка SSE уведомления:', error);
      }
    });

  } catch (error) {
    console.error('❌ Ошибка MCP:', error);
    res.status(500).json({
      jsonrpc: '2.0',
      error: { code: -32603, message: 'Internal server error' },
      id: request.id || null
    });
  }
});

// Страница добавления услуг
app.get('/add-service', (req, res) => {
  const fs = require('fs');
  const path = require('path');

  try {
    const htmlPath = path.join(__dirname, 'add-service.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    res.send(htmlContent);
  } catch (error) {
    console.error('❌ Ошибка загрузки страницы добавления услуг:', error);
    res.status(500).send(`
      <h1>❌ Ошибка</h1>
      <p>Не удалось загрузить страницу добавления услуг.</p>
      <p>Убедитесь, что файл add-service.html находится в корневой папке проекта.</p>
      <p><a href="/">← Вернуться на главную</a></p>
    `);
  }
});

// Mistral чат
app.get('/chat', (req, res) => {
  const fs = require('fs');
  const path = require('path');

  try {
    const htmlPath = path.join(__dirname, 'simple-mistral-chat.html');
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');

    // Автоматически обновляем URL на текущий ngrok URL
    const currentMcpUrl = ngrokUrl ? `${ngrokUrl}/mcp` : 'http://localhost:3000/mcp';
    htmlContent = htmlContent.replace(
      /const MCP_SERVER_URL = '[^']*';/,
      `const MCP_SERVER_URL = '${currentMcpUrl}';`
    );

    res.send(htmlContent);
  } catch (error) {
    console.error('❌ Ошибка загрузки Mistral чата:', error);
    res.status(500).send(`
      <h1>❌ Ошибка</h1>
      <p>Не удалось загрузить Mistral чат.</p>
      <p>Убедитесь, что файл simple-mistral-chat.html находится в корневой папке проекта.</p>
      <p><a href="/">← Вернуться на главную</a></p>
    `);
  }
});

// Тест страница
app.get('/test', (req, res) => {
  res.send(`
    <h1>🧪 Тест ngrok SSE MCP Сервера</h1>
    <p><strong>Публичный URL:</strong> ${ngrokUrl}</p>
    <div id="status">Подключение...</div>
    <div id="messages"></div>
    
    <script>
      const eventSource = new EventSource('/mcp');
      const status = document.getElementById('status');
      const messages = document.getElementById('messages');
      
      eventSource.onopen = () => {
        status.textContent = '✅ SSE соединение через ngrok установлено';
      };
      
      eventSource.addEventListener('endpoint', (event) => {
        const data = JSON.parse(event.data);
        messages.innerHTML += '<p><strong>Endpoint:</strong> ' + data.uri + '</p>';
      });
      
      eventSource.addEventListener('message', (event) => {
        const data = JSON.parse(event.data);
        messages.innerHTML += '<p><strong>Уведомление:</strong> ' + data.params.message + '</p>';
      });
      
      eventSource.onerror = () => {
        status.textContent = '❌ Ошибка SSE соединения';
      };
    </script>
  `);
});

// API для добавления новых услуг
app.post('/api/services', (req, res) => {
  try {
    const { id, title, description, price, duration, technologies } = req.body;

    // Валидация
    if (!id || !title || !description || !price || !duration) {
      return res.status(400).json({
        success: false,
        error: 'Все поля обязательны для заполнения'
      });
    }

    // Проверяем, что услуга с таким ID не существует
    if (services[id]) {
      return res.status(409).json({
        success: false,
        error: 'Услуга с таким ID уже существует'
      });
    }

    // Добавляем новую услугу
    services[id] = {
      title,
      description,
      price,
      duration,
      technologies: technologies || []
    };

    // Сохраняем в файл
    saveServices(services);

    console.log(`✅ [${new Date().toLocaleTimeString()}] Добавлена новая услуга: ${title} (${id})`);

    res.json({
      success: true,
      message: 'Услуга успешно добавлена и сохранена',
      service: services[id]
    });

  } catch (error) {
    console.error('❌ Ошибка добавления услуги:', error);
    res.status(500).json({
      success: false,
      error: 'Внутренняя ошибка сервера'
    });
  }
});

// API для получения всех услуг
app.get('/api/services', (req, res) => {
  res.json({
    success: true,
    services: services,
    count: Object.keys(services).length
  });
});

// API для удаления услуги
app.delete('/api/services/:id', (req, res) => {
  try {
    const { id } = req.params;

    if (!services[id]) {
      return res.status(404).json({
        success: false,
        error: 'Услуга не найдена'
      });
    }

    const deletedService = services[id];
    delete services[id];

    // Сохраняем в файл
    saveServices(services);

    console.log(`🗑️ [${new Date().toLocaleTimeString()}] Удалена услуга: ${deletedService.title} (${id})`);

    res.json({
      success: true,
      message: 'Услуга успешно удалена',
      deletedService: deletedService
    });

  } catch (error) {
    console.error('❌ Ошибка удаления услуги:', error);
    res.status(500).json({
      success: false,
      error: 'Внутренняя ошибка сервера'
    });
  }
});

// WHOIS страница
app.get('/whois', (req, res) => {
  try {
    const htmlPath = path.join(__dirname, 'whois-checker.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    res.send(htmlContent);
  } catch (error) {
    console.error('❌ Ошибка загрузки WHOIS страницы:', error);
    res.status(500).send(`
      <h1>❌ Ошибка</h1>
      <p>Не удалось загрузить страницу WHOIS проверки.</p>
      <p>Убедитесь, что файл whois-checker.html находится в корневой папке проекта.</p>
      <p><a href="/">← Вернуться на главную</a></p>
    `);
  }
});

// API для WHOIS проверки домена
app.get('/api/whois/:domain', async (req, res) => {
  const { domain } = req.params;

  try {
    console.log(`🔍 API запрос whois для: ${domain}`);
    const whoisResult = await executeWhois(domain);
    const parsedResult = parseWhoisResult(whoisResult, domain);

    res.json({
      success: true,
      data: parsedResult
    });
  } catch (error) {
    console.error(`❌ Ошибка whois для ${domain}:`, error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Статистика
app.get('/stats', (req, res) => {
  res.json({
    uptime: process.uptime(),
    sseConnections: sseConnections.size,
    services: Object.keys(services),
    serverType: 'ngrok SSE MCP Server',
    publicUrl: ngrokUrl,
    localUrl: 'http://localhost:3000',
    timestamp: new Date().toISOString(),
    connections: Array.from(sseConnections.values()).map(conn => ({
      ip: conn.ip,
      connected: new Date(conn.connected).toISOString()
    }))
  });
});

const PORT = 3000;

// Запуск сервера
const server = app.listen(PORT, () => {
  console.log(`🌐 ngrok SSE MCP Server запущен на порту ${PORT}`);
  console.log(`📍 Локальный URL: http://localhost:${PORT}`);
});

// Запуск ngrok
ngrok.connect({ 
  addr: PORT, 
  authtoken_from_env: true,
  domain: process.env.NGROK_DOMAIN // Можно указать кастомный домен
})
.then(listener => {
  ngrokUrl = listener.url();
  console.log(`🌍 Публичный URL: ${ngrokUrl}`);
  console.log(`🔗 MCP Endpoint: ${ngrokUrl}/mcp`);
  console.log(`🧪 Тест: ${ngrokUrl}/test`);
  console.log(`📊 Статистика: ${ngrokUrl}/stats`);
  console.log(`\n📋 Конфигурация для удаленного Augment:`);
  console.log(`{
  "mcpServers": {
    "remote_services": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/sdk", "client", "${ngrokUrl}/mcp"]
    }
  }
}`);
})
.catch(error => {
  console.error('❌ Ошибка ngrok:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Остановка ngrok сервера...');
  
  sseConnections.forEach(({ res }) => {
    try {
      res.end();
    } catch (error) {
      console.error('Ошибка закрытия SSE:', error);
    }
  });
  
  server.close(() => {
    console.log('✅ ngrok сервер остановлен');
    process.exit(0);
  });
});
