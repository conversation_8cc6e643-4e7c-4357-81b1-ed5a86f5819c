# My Services MCP Server

MCP сервер для предоставления информации о ваших услугах AI ассистентам.

## Установка

```bash
npm install -g @your-username/my-services-mcp
```

## Использование в VS Code

Добавьте в конфигурацию MCP:

```json
{
  "mcpServers": {
    "my_services": {
      "command": "npx",
      "args": [
        "-y",
        "@your-username/my-services-mcp"
      ]
    }
  }
}
```

## Доступные возможности

### Ресурсы
- `services://catalog` - каталог всех услуг
- `services://{serviceId}` - детали конкретной услуги
- `services://prices` - прайс-лист

### Инструменты
- `search-services` - поиск услуг
- `estimate-cost` - расчет стоимости
- `create-proposal` - создание предложения

### Промпты
- `consultation-response` - ответ на запрос клиента

## Настройка

Отредактируйте файл `src/index.ts` и добавьте ваши реальные услуги и контакты.

## Разработка

```bash
npm install
npm run dev
```

## Публикация

```bash
npm run build
npm publish
```
