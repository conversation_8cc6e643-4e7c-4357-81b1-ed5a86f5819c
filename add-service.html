<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>➕ Добавить новую услугу - MCP Services</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card-custom {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .tech-tag {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            margin: 3px;
            display: inline-block;
            font-size: 0.9em;
        }
        .tech-tag .remove {
            margin-left: 8px;
            cursor: pointer;
            font-weight: bold;
        }
        .status-message {
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        .server-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            color: white;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Информация о сервере -->
                <div class="server-info text-center">
                    <h1 class="mb-3"><i class="bi bi-plus-circle"></i> Добавить новую услугу</h1>
                    <p class="mb-2"><strong>🌐 Сервер:</strong> <span id="serverUrl">Загрузка...</span></p>
                    <p class="mb-0"><strong>📊 Услуг в системе:</strong> <span id="servicesCount">Загрузка...</span></p>
                </div>

                <!-- Основная форма -->
                <div class="card card-custom">
                    <div class="card-body p-5">
                        <form id="serviceForm">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="serviceId" class="form-label fw-bold">
                                        <i class="bi bi-tag"></i> ID услуги
                                    </label>
                                    <input type="text" class="form-control" id="serviceId" 
                                           placeholder="web-development" required>
                                    <div class="form-text">Уникальный идентификатор (только латиница, цифры, дефисы)</div>
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <label for="serviceTitle" class="form-label fw-bold">
                                        <i class="bi bi-card-heading"></i> Название услуги
                                    </label>
                                    <input type="text" class="form-control" id="serviceTitle" 
                                           placeholder="Веб-разработка" required>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="serviceDescription" class="form-label fw-bold">
                                    <i class="bi bi-file-text"></i> Описание
                                </label>
                                <textarea class="form-control" id="serviceDescription" rows="3" 
                                          placeholder="Подробное описание услуги..." required></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="servicePrice" class="form-label fw-bold">
                                        <i class="bi bi-currency-dollar"></i> Стоимость
                                    </label>
                                    <input type="text" class="form-control" id="servicePrice" 
                                           placeholder="от 50,000 руб" required>
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <label for="serviceDuration" class="form-label fw-bold">
                                        <i class="bi bi-clock"></i> Срок выполнения
                                    </label>
                                    <input type="text" class="form-control" id="serviceDuration" 
                                           placeholder="2-4 недели" required>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="techInput" class="form-label fw-bold">
                                    <i class="bi bi-gear"></i> Технологии
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="techInput" 
                                           placeholder="Введите технологию и нажмите Enter">
                                    <button type="button" class="btn btn-outline-primary" id="addTechBtn">
                                        <i class="bi bi-plus"></i> Добавить
                                    </button>
                                </div>
                                <div id="techTags" class="mt-3"></div>
                            </div>

                            <!-- Сообщения -->
                            <div id="successMessage" class="status-message alert alert-success">
                                <i class="bi bi-check-circle"></i> <span id="successText"></span>
                            </div>
                            
                            <div id="errorMessage" class="status-message alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i> <span id="errorText"></span>
                            </div>

                            <!-- Кнопки -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="button" class="btn btn-outline-secondary btn-custom me-md-2" id="resetBtn">
                                    <i class="bi bi-arrow-clockwise"></i> Очистить
                                </button>
                                <button type="submit" class="btn btn-primary btn-custom" id="submitBtn">
                                    <i class="bi bi-plus-circle"></i> Добавить услугу
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Список существующих услуг -->
                <div class="card card-custom mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-list"></i> Существующие услуги</h5>
                    </div>
                    <div class="card-body">
                        <div id="existingServices">Загрузка...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let technologies = [];
        let serverUrl = '';

        // Определяем URL сервера
        function getServerUrl() {
            const currentUrl = window.location.href;
            if (currentUrl.includes('ngrok')) {
                return window.location.origin;
            } else {
                return 'http://localhost:3000';
            }
        }

        // Загрузка информации о сервере
        async function loadServerInfo() {
            try {
                serverUrl = getServerUrl();
                document.getElementById('serverUrl').textContent = serverUrl;
                
                const response = await fetch(`${serverUrl}/api/services`);
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('servicesCount').textContent = data.count;
                    displayExistingServices(data.services);
                }
            } catch (error) {
                console.error('Ошибка загрузки информации:', error);
                document.getElementById('serverUrl').textContent = 'Ошибка подключения';
                document.getElementById('servicesCount').textContent = 'Н/Д';
            }
        }

        // Отображение существующих услуг
        function displayExistingServices(services) {
            const container = document.getElementById('existingServices');
            
            if (Object.keys(services).length === 0) {
                container.innerHTML = '<p class="text-muted">Пока нет добавленных услуг</p>';
                return;
            }
            
            let html = '<div class="row">';
            Object.entries(services).forEach(([id, service]) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="fw-bold text-primary">${service.title}</h6>
                            <p class="small text-muted mb-1">ID: ${id}</p>
                            <p class="small mb-1">${service.price} • ${service.duration}</p>
                            <div class="small">
                                ${service.technologies.map(tech => 
                                    `<span class="badge bg-secondary me-1">${tech}</span>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        // Добавление технологии
        function addTechnology() {
            const input = document.getElementById('techInput');
            const tech = input.value.trim();
            
            if (tech && !technologies.includes(tech)) {
                technologies.push(tech);
                updateTechTags();
                input.value = '';
            }
        }

        // Обновление отображения технологий
        function updateTechTags() {
            const container = document.getElementById('techTags');
            container.innerHTML = technologies.map(tech => 
                `<span class="tech-tag">${tech}<span class="remove" onclick="removeTech('${tech}')">×</span></span>`
            ).join('');
        }

        // Удаление технологии
        function removeTech(tech) {
            technologies = technologies.filter(t => t !== tech);
            updateTechTags();
        }

        // Показ сообщения
        function showMessage(type, text) {
            const successMsg = document.getElementById('successMessage');
            const errorMsg = document.getElementById('errorMessage');
            
            successMsg.style.display = 'none';
            errorMsg.style.display = 'none';
            
            if (type === 'success') {
                document.getElementById('successText').textContent = text;
                successMsg.style.display = 'block';
            } else {
                document.getElementById('errorText').textContent = text;
                errorMsg.style.display = 'block';
            }
            
            setTimeout(() => {
                successMsg.style.display = 'none';
                errorMsg.style.display = 'none';
            }, 5000);
        }

        // Отправка формы
        async function submitForm(event) {
            event.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Добавление...';
            submitBtn.disabled = true;
            
            try {
                const formData = {
                    id: document.getElementById('serviceId').value.trim(),
                    title: document.getElementById('serviceTitle').value.trim(),
                    description: document.getElementById('serviceDescription').value.trim(),
                    price: document.getElementById('servicePrice').value.trim(),
                    duration: document.getElementById('serviceDuration').value.trim(),
                    technologies: technologies
                };
                
                const response = await fetch(`${serverUrl}/api/services`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('success', result.message);
                    document.getElementById('serviceForm').reset();
                    technologies = [];
                    updateTechTags();
                    loadServerInfo(); // Обновляем список услуг
                } else {
                    showMessage('error', result.error);
                }
                
            } catch (error) {
                console.error('Ошибка отправки:', error);
                showMessage('error', 'Ошибка подключения к серверу');
            } finally {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // Очистка формы
        function resetForm() {
            document.getElementById('serviceForm').reset();
            technologies = [];
            updateTechTags();
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            loadServerInfo();
            
            // Обработчики событий
            document.getElementById('serviceForm').addEventListener('submit', submitForm);
            document.getElementById('addTechBtn').addEventListener('click', addTechnology);
            document.getElementById('resetBtn').addEventListener('click', resetForm);
            
            // Enter для добавления технологии
            document.getElementById('techInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addTechnology();
                }
            });
            
            // Валидация ID (только латиница, цифры, дефисы)
            document.getElementById('serviceId').addEventListener('input', function(e) {
                e.target.value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
            });
        });
    </script>
</body>
</html>
